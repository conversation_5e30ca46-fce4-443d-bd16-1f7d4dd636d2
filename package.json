{"name": "craftpress", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=\"--no-deprecation --max-old-space-size=8000\" next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev -p 4000", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@payloadcms/db-postgres": "3.50.0", "@payloadcms/next": "3.50.0", "@payloadcms/payload-cloud": "3.50.0", "@payloadcms/plugin-multi-tenant": "^3.50.0", "@payloadcms/richtext-lexical": "3.50.0", "@payloadcms/storage-s3": "^3.50.0", "@payloadcms/ui": "3.50.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^10.0.0", "graphql": "^16.11.0", "lucide-react": "^0.539.0", "next": "15.4.6", "next-themes": "^0.4.6", "payload": "3.50.0", "react": "19.1.1", "react-dom": "19.1.1", "sharp": "0.34.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@types/node": "^24.2.1", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "@vitejs/plugin-react": "5.0.0", "dotenv": "17.2.1", "eslint": "^9.33.0", "eslint-config-next": "15.4.6", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "5.9.2"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}