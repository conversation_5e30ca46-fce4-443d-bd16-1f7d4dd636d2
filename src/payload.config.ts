import { s3Storage } from '@payloadcms/storage-s3'
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Posts } from './collections/Posts'
import { Pages } from './collections/Pages'
import { Authors } from './collections/Authors'
import { Categories } from './collections/Categories'
import { Tags } from './collections/Tags'
import { Tenants } from './collections/Tenants'
import { AdCampaigns } from './collections/AdCampaigns'
import { AdCreatives } from './collections/AdCreatives'
import { PricingRules } from './collections/PricingRules'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [
    Users,
    Media,
    Posts,
    Pages,
    Authors,
    Categories,
    Tags,
    Tenants,
    // Sponsorship Platform
    AdCampaigns,
    AdCreatives,
    PricingRules,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL || '',
    },
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    multiTenantPlugin({
      collections: {
        posts: {},
        pages: {},
        authors: {},
        categories: {},
        tags: {},
        media: {
          // Allow media to be shared across sites
          useTenantAccess: false,
        },
      },
      // Function to determine if a user has access to all sites (super admin)
      userHasAccessToAllTenants: (user) => {
        return user?.roles?.includes('super-admin') || false
      },
      // Customize the site selector label
      tenantSelectorLabel: 'Select Site',

      // Enable debug mode to see site fields in admin
      debug: process.env.NODE_ENV === 'development',
    }),
    // Cloudflare R2 Storage Configuration
    s3Storage({
      collections: {
        media: {
          // Multi-tenant organization: each site gets its own folder
          prefix: ({ req }) => {
            // Get the current tenant/site from the request
            const tenant = req?.user?.tenants?.[0] || req?.tenant
            if (tenant && typeof tenant === 'object' && 'slug' in tenant) {
              return `sites/${tenant.slug}`
            }
            // Fallback for shared media or when no tenant is selected
            return 'shared'
          },
        },
      },
      bucket: process.env.S3_BUCKET || '',
      config: {
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        region: process.env.S3_REGION || 'auto',
        endpoint: process.env.S3_ENDPOINT || '',
        forcePathStyle: true, // Required for Cloudflare R2
      },
    }),
  ],
})
