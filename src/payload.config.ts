// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Posts } from './collections/Posts'
import { Pages } from './collections/Pages'
import { Authors } from './collections/Authors'
import { Categories } from './collections/Categories'
import { Tags } from './collections/Tags'
import { Tenants } from './collections/Tenants'
import { AdCampaigns } from './collections/AdCampaigns'
import { AdCreatives } from './collections/AdCreatives'
import { PricingRules } from './collections/PricingRules'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [
    Users,
    Media,
    Posts,
    Pages,
    Authors,
    Categories,
    Tags,
    Tenants,
    // Sponsorship Platform
    AdCampaigns,
    AdCreatives,
    PricingRules,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    multiTenantPlugin({
      collections: {
        posts: {},
        pages: {},
        authors: {},
        categories: {},
        tags: {},
        media: {
          // Allow media to be shared across sites
          useTenantAccess: false,
        },
      },
      // Function to determine if a user has access to all sites (super admin)
      userHasAccessToAllTenants: (user) => {
        return user?.roles?.includes('super-admin') || false
      },
      // Customize the site selector label
      tenantSelectorLabel: 'Select Site',

      // Enable debug mode to see site fields in admin
      debug: process.env.NODE_ENV === 'development',
    }),
    // storage-adapter-placeholder
  ],
})
