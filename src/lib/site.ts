import { getPayload } from 'payload'
import { headers } from 'next/headers'
import config from '@/payload.config'

export interface Site {
  id: string
  name: string
  slug: string
  domain: string
  domains?: Array<{ domain: string }>
  isActive: boolean
  themeConfig?: {
    primaryColor?: string
    logo?: {
      id: string
      url: string
      alt: string
      width?: number
      height?: number
    }
    favicon?: {
      id: string
      url: string
      alt: string
    }
  }
  seoDefaults?: {
    title?: string
    description?: string
    keywords?: string
    socialImage?: {
      id: string
      url: string
      alt: string
    }
  }
}

export async function getCurrentSite(): Promise<Site | null> {
  try {
    const headersList = await headers()
    const host = headersList.get('host')

    if (!host) return null

    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // For development, if host is localhost, get the first active site
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      try {
        const sites = await payload.find({
          collection: 'tenants',
          where: {
            isActive: { equals: true },
          },
          limit: 1,
          sort: 'createdAt',
        })

        if (sites.docs.length > 0) {
          const site = sites.docs[0] as any
          return {
            id: site.id,
            name: site.name,
            slug: site.slug,
            domain: site.domain,
            domains: site.domains,
            isActive: site.isActive,
            themeConfig: site.themeConfig,
            seoDefaults: site.seoDefaults,
          }
        }
      } catch (dbError) {
        console.error('Database error when fetching tenants:', dbError)
        // Return a default site for development when no tenants exist
        return {
          id: 'default',
          name: 'Tech News Daily',
          slug: 'tech-news-daily',
          domain: 'localhost:4000',
          isActive: true,
          seoDefaults: {
            title: 'Tech News Daily - Latest Technology News',
            description:
              'Stay updated with the latest technology news, reviews, and insights from the world of tech.',
          },
        }
      }
    }

    // Find site by domain
    const sites = await payload.find({
      collection: 'tenants',
      where: {
        or: [{ domain: { equals: host } }, { 'domains.domain': { equals: host } }],
      },
      limit: 1,
    })

    if (sites.docs.length === 0) return null

    const site = sites.docs[0] as any

    return {
      id: site.id,
      name: site.name,
      slug: site.slug,
      domain: site.domain,
      domains: site.domains,
      isActive: site.isActive,
      themeConfig: site.themeConfig,
      seoDefaults: site.seoDefaults,
    }
  } catch (error) {
    console.error('Error fetching current site:', error)
    // Return a default site for development when there are issues
    return {
      id: 'default',
      name: 'Tech News Daily',
      slug: 'tech-news-daily',
      domain: 'localhost:4000',
      isActive: true,
      seoDefaults: {
        title: 'Tech News Daily - Latest Technology News',
        description:
          'Stay updated with the latest technology news, reviews, and insights from the world of tech.',
      },
    }
  }
}

export async function getSiteBySlug(slug: string): Promise<Site | null> {
  try {
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    const sites = await payload.find({
      collection: 'tenants',
      where: {
        slug: { equals: slug },
      },
      limit: 1,
    })

    if (sites.docs.length === 0) return null

    const site = sites.docs[0] as any

    return {
      id: site.id,
      name: site.name,
      slug: site.slug,
      domain: site.domain,
      domains: site.domains,
      isActive: site.isActive,
      themeConfig: site.themeConfig,
      seoDefaults: site.seoDefaults,
    }
  } catch (error) {
    console.error('Error fetching site by slug:', error)
    return null
  }
}

export function generateSiteThemeCSS(site: Site): string {
  if (!site.themeConfig?.primaryColor) return ''

  // Convert hex to RGB values for CSS custom properties
  const hex = site.themeConfig.primaryColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)

  // Generate color palette based on primary color
  return `
    :root {
      --brand-50: ${Math.min(255, r + 40)} ${Math.min(255, g + 40)} ${Math.min(255, b + 40)};
      --brand-100: ${Math.min(255, r + 30)} ${Math.min(255, g + 30)} ${Math.min(255, b + 30)};
      --brand-200: ${Math.min(255, r + 20)} ${Math.min(255, g + 20)} ${Math.min(255, b + 20)};
      --brand-300: ${Math.min(255, r + 10)} ${Math.min(255, g + 10)} ${Math.min(255, b + 10)};
      --brand-400: ${r} ${g} ${b};
      --brand-500: ${Math.max(0, r - 10)} ${Math.max(0, g - 10)} ${Math.max(0, b - 10)};
      --brand-600: ${Math.max(0, r - 20)} ${Math.max(0, g - 20)} ${Math.max(0, b - 20)};
      --brand-700: ${Math.max(0, r - 30)} ${Math.max(0, g - 30)} ${Math.max(0, b - 30)};
      --brand-800: ${Math.max(0, r - 40)} ${Math.max(0, g - 40)} ${Math.max(0, b - 40)};
      --brand-900: ${Math.max(0, r - 50)} ${Math.max(0, g - 50)} ${Math.max(0, b - 50)};
      --brand-950: ${Math.max(0, r - 60)} ${Math.max(0, g - 60)} ${Math.max(0, b - 60)};
    }
  `
}
