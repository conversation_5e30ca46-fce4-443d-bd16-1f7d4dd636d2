import { getPayload } from 'payload'
import config from '@/payload.config'

export interface Post {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: any
  featuredImage?: {
    id: string
    url: string
    alt: string
    width?: number
    height?: number
  }
  author?: {
    id: string
    name: string
    bio?: string
    avatar?: {
      url: string
      alt: string
    }
  }
  categories?: Array<{
    id: string
    name: string
    slug: string
    color?: string
  }>
  tags?: Array<{
    id: string
    name: string
    slug: string
  }>
  publishedAt: string
  readingTime?: number
  status: 'draft' | 'published' | 'scheduled' | 'archived'
  isFeatured?: boolean
  seo?: {
    title?: string
    description?: string
    keywords?: string
    socialImage?: {
      url: string
      alt: string
    }
  }
}

export async function getPostsForSite(
  siteId: string,
  options: {
    limit?: number
    featured?: boolean
    status?: 'published' | 'draft' | 'scheduled' | 'archived'
  } = {},
): Promise<Post[]> {
  try {
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    const where: any = {
      tenant: { equals: siteId },
    }

    if (options.status) {
      where.status = { equals: options.status }
    }

    if (options.featured !== undefined) {
      where.isFeatured = { equals: options.featured }
    }

    const posts = await payload.find({
      collection: 'posts',
      where,
      limit: options.limit || 10,
      sort: '-publishedAt',
      populate: {
        author: true,
        categories: true,
        tags: true,
        featuredImage: true,
        'seo.socialImage': true,
      },
    })

    return posts.docs.map((post: any) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      featuredImage: post.featuredImage,
      author: post.author,
      categories: post.categories,
      tags: post.tags,
      publishedAt: post.publishedAt,
      readingTime: post.readingTime,
      status: post.status,
      isFeatured: post.isFeatured,
      seo: post.seo,
    }))
  } catch (error) {
    console.error('Error fetching posts for site:', error)
    return []
  }
}

export async function getPostBySlug(slug: string, siteId: string): Promise<Post | null> {
  try {
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    const posts = await payload.find({
      collection: 'posts',
      where: {
        and: [
          { slug: { equals: slug } },
          { tenant: { equals: siteId } },
          { status: { equals: 'published' } },
        ],
      },
      limit: 1,
      populate: {
        author: true,
        categories: true,
        tags: true,
        featuredImage: true,
        'seo.socialImage': true,
      },
    })

    if (posts.docs.length === 0) return null

    const post = posts.docs[0] as any

    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      featuredImage: post.featuredImage,
      author: post.author,
      categories: post.categories,
      tags: post.tags,
      publishedAt: post.publishedAt,
      readingTime: post.readingTime,
      status: post.status,
      isFeatured: post.isFeatured,
      seo: post.seo,
    }
  } catch (error) {
    console.error('Error fetching post by slug:', error)
    return null
  }
}
