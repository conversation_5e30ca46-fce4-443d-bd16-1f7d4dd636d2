/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    posts: Post;
    pages: Page;
    authors: Author;
    categories: Category;
    tags: Tag;
    tenants: Tenant;
    'ad-campaigns': AdCampaign;
    'ad-creatives': AdCreative;
    'pricing-rules': PricingRule;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    pages: PagesSelect<false> | PagesSelect<true>;
    authors: AuthorsSelect<false> | AuthorsSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    tags: TagsSelect<false> | TagsSelect<true>;
    tenants: TenantsSelect<false> | TenantsSelect<true>;
    'ad-campaigns': AdCampaignsSelect<false> | AdCampaignsSelect<true>;
    'ad-creatives': AdCreativesSelect<false> | AdCreativesSelect<true>;
    'pricing-rules': PricingRulesSelect<false> | PricingRulesSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  roles: ('super-admin' | 'admin' | 'editor' | 'author' | 'sponsor')[];
  /**
   * Additional information for sponsor accounts
   */
  sponsorProfile?: {
    /**
     * Official company name for billing and contracts
     */
    companyName?: string | null;
    /**
     * Company website URL
     */
    website?: string | null;
    /**
     * Primary contact person for campaigns
     */
    contactPerson?: string | null;
    /**
     * Contact phone number
     */
    phone?: string | null;
    billingAddress?: {
      street?: string | null;
      city?: string | null;
      state?: string | null;
      zipCode?: string | null;
      country?: string | null;
    };
    /**
     * Whether this sponsor is approved for auto-approval of campaigns
     */
    isApproved?: boolean | null;
    /**
     * Internal notes about this sponsor (not visible to sponsor)
     */
    notes?: string | null;
  };
  tenants?:
    | {
        tenant: number | Tenant;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants".
 */
export interface Tenant {
  id: number;
  /**
   * Display name for this site/tenant
   */
  name: string;
  /**
   * URL-friendly identifier (e.g., "my-blog")
   */
  slug: string;
  /**
   * Primary domain for this site (e.g., "myblog.com")
   */
  domain: string;
  /**
   * Additional domains/aliases for this site
   */
  domains?:
    | {
        domain: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Whether this site is active and accessible
   */
  isActive?: boolean | null;
  themeConfig?: {
    /**
     * Hex color code (e.g., #3B82F6)
     */
    primaryColor?: string | null;
    logo?: (number | null) | Media;
    favicon?: (number | null) | Media;
  };
  seoDefaults?: {
    title?: string | null;
    description?: string | null;
    /**
     * Comma-separated keywords
     */
    keywords?: string | null;
    ogImage?: (number | null) | Media;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  tenant?: (number | null) | Tenant;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: number;
  tenant?: (number | null) | Tenant;
  title: string;
  /**
   * Used in URLs (e.g., /blog/my-awesome-post)
   */
  slug: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief summary for previews and SEO
   */
  excerpt?: string | null;
  featuredImage?: (number | null) | Media;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  publishedAt?: string | null;
  author: number | Author;
  category: number | Category;
  tags?: (number | Tag)[] | null;
  /**
   * Show in featured posts section
   */
  isFeatured?: boolean | null;
  /**
   * Estimated reading time in minutes
   */
  readingTime?: number | null;
  seo?: {
    /**
     * Override the post title for search engines
     */
    metaTitle?: string | null;
    /**
     * Description for search engines (150-160 characters)
     */
    metaDescription?: string | null;
    metaImage?: (number | null) | Media;
    /**
     * Comma-separated keywords for SEO
     */
    keywords?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors".
 */
export interface Author {
  id: number;
  tenant?: (number | null) | Tenant;
  name: string;
  email: string;
  bio?: string | null;
  avatar?: (number | null) | Media;
  role: 'author' | 'editor' | 'admin';
  socialLinks?: {
    twitter?: string | null;
    linkedin?: string | null;
    website?: string | null;
  };
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: number;
  tenant?: (number | null) | Tenant;
  name: string;
  /**
   * Used in URLs (e.g., /category/technology)
   */
  slug: string;
  description?: string | null;
  /**
   * Hex color code (e.g., #3B82F6)
   */
  color?: string | null;
  featuredImage?: (number | null) | Media;
  /**
   * Leave empty for top-level categories
   */
  parent?: (number | null) | Category;
  isVisible?: boolean | null;
  /**
   * Lower numbers appear first
   */
  sortOrder?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags".
 */
export interface Tag {
  id: number;
  tenant?: (number | null) | Tenant;
  name: string;
  /**
   * Used in URLs (e.g., /tag/javascript)
   */
  slug: string;
  description?: string | null;
  /**
   * Hex color code (e.g., #10B981)
   */
  color?: string | null;
  isVisible?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: number;
  tenant?: (number | null) | Tenant;
  title: string;
  /**
   * Used in URLs (e.g., /about-us)
   */
  slug: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief description for SEO and previews
   */
  excerpt?: string | null;
  featuredImage?: (number | null) | Media;
  status: 'draft' | 'published' | 'archived';
  publishedAt?: string | null;
  author: number | Author;
  seo?: {
    /**
     * Override the page title for search engines
     */
    metaTitle?: string | null;
    /**
     * Description for search engines (150-160 characters)
     */
    metaDescription?: string | null;
    metaImage?: (number | null) | Media;
  };
  showInNavigation?: boolean | null;
  /**
   * Lower numbers appear first
   */
  navigationOrder?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ad-campaigns".
 */
export interface AdCampaign {
  id: number;
  /**
   * Internal name for this advertising campaign
   */
  name: string;
  /**
   * The sponsor who owns this campaign
   */
  sponsor: number | User;
  /**
   * Current status of the campaign
   */
  status: 'draft' | 'pending' | 'approved' | 'active' | 'paused' | 'completed' | 'rejected';
  /**
   * When the campaign should start running
   */
  startDate: string;
  /**
   * When the campaign should stop running
   */
  endDate: string;
  /**
   * Total budget for this campaign in USD
   */
  totalBudget: number;
  targeting: {
    /**
     * Which sites should display this campaign
     */
    sites?: (number | Tenant)[] | null;
    /**
     * How to target content on the selected sites
     */
    targetingType: 'site-wide' | 'posts' | 'pages' | 'categories' | 'tags';
    /**
     * Specific posts to target
     */
    specificPosts?: (number | Post)[] | null;
    /**
     * Specific pages to target
     */
    specificPages?: (number | Page)[] | null;
    /**
     * Target posts in these categories
     */
    categories?: (number | Category)[] | null;
    /**
     * Target posts with these tags
     */
    tags?: (number | Tag)[] | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ad-creatives".
 */
export interface AdCreative {
  id: number;
  /**
   * Internal title for this ad creative
   */
  title: string;
  /**
   * Which campaign this creative belongs to
   */
  campaign: number | AdCampaign;
  /**
   * Type of advertisement
   */
  type: 'text' | 'banner' | 'text-link';
  /**
   * Approval status of this creative
   */
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  /**
   * Content for text-based advertisements
   */
  textContent?: {
    /**
     * Main headline (max 60 characters)
     */
    headline: string;
    /**
     * Additional description text (max 200 characters)
     */
    description?: string | null;
    /**
     * Button text (max 20 characters, e.g., "Learn More")
     */
    callToAction?: string | null;
  };
  /**
   * Content for banner advertisements
   */
  bannerContent?: {
    /**
     * Banner image (recommended: 728x90, 300x250, or 320x50)
     */
    image: number | Media;
    /**
     * Alternative text for accessibility
     */
    altText: string;
  };
  link: {
    /**
     * Where users go when they click the ad
     */
    url: string;
    /**
     * UTM source for tracking (auto-filled)
     */
    utmSource?: string | null;
    /**
     * UTM medium for tracking (auto-filled)
     */
    utmMedium?: string | null;
    /**
     * UTM campaign for tracking (optional)
     */
    utmCampaign?: string | null;
  };
  /**
   * Internal notes about this creative (not visible to sponsor)
   */
  adminNotes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pricing-rules".
 */
export interface PricingRule {
  id: number;
  /**
   * Internal name for this pricing rule
   */
  name: string;
  /**
   * Specific site (leave empty for global rule)
   */
  site?: (number | null) | Tenant;
  /**
   * Where the ad will be placed
   */
  placementType:
    | 'site-wide'
    | 'homepage'
    | 'post-content'
    | 'page-content'
    | 'category-pages'
    | 'tag-pages'
    | 'sidebar'
    | 'header'
    | 'footer';
  /**
   * Base price per day in USD
   */
  basePrice: number;
  /**
   * Factors that adjust the base price
   */
  multipliers?: {
    /**
     * Price multiplier for weekends (1.0 = no change, 1.5 = 50% more)
     */
    weekendMultiplier?: number | null;
    /**
     * Price multiplier for holidays
     */
    holidayMultiplier?: number | null;
    /**
     * Price multiplier for peak seasons
     */
    peakSeasonMultiplier?: number | null;
    /**
     * Price multiplier when demand is high
     */
    demandMultiplier?: number | null;
  };
  minimums?: {
    /**
     * Minimum campaign duration in days
     */
    minimumDays?: number | null;
    /**
     * Minimum total budget required
     */
    minimumBudget?: number | null;
  };
  /**
   * Discounts for longer campaigns
   */
  discounts?: {
    /**
     * Discount for campaigns 7+ days (percentage)
     */
    weeklyDiscount?: number | null;
    /**
     * Discount for campaigns 30+ days (percentage)
     */
    monthlyDiscount?: number | null;
    /**
     * Discount for campaigns 90+ days (percentage)
     */
    quarterlyDiscount?: number | null;
  };
  /**
   * Whether this pricing rule is currently active
   */
  isActive?: boolean | null;
  /**
   * Rule priority (lower numbers = higher priority)
   */
  priority?: number | null;
  /**
   * Internal notes about this pricing rule
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'posts';
        value: number | Post;
      } | null)
    | ({
        relationTo: 'pages';
        value: number | Page;
      } | null)
    | ({
        relationTo: 'authors';
        value: number | Author;
      } | null)
    | ({
        relationTo: 'categories';
        value: number | Category;
      } | null)
    | ({
        relationTo: 'tags';
        value: number | Tag;
      } | null)
    | ({
        relationTo: 'tenants';
        value: number | Tenant;
      } | null)
    | ({
        relationTo: 'ad-campaigns';
        value: number | AdCampaign;
      } | null)
    | ({
        relationTo: 'ad-creatives';
        value: number | AdCreative;
      } | null)
    | ({
        relationTo: 'pricing-rules';
        value: number | PricingRule;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  roles?: T;
  sponsorProfile?:
    | T
    | {
        companyName?: T;
        website?: T;
        contactPerson?: T;
        phone?: T;
        billingAddress?:
          | T
          | {
              street?: T;
              city?: T;
              state?: T;
              zipCode?: T;
              country?: T;
            };
        isApproved?: T;
        notes?: T;
      };
  tenants?:
    | T
    | {
        tenant?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  tenant?: T;
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  slug?: T;
  content?: T;
  excerpt?: T;
  featuredImage?: T;
  status?: T;
  publishedAt?: T;
  author?: T;
  category?: T;
  tags?: T;
  isFeatured?: T;
  readingTime?: T;
  seo?:
    | T
    | {
        metaTitle?: T;
        metaDescription?: T;
        metaImage?: T;
        keywords?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  slug?: T;
  content?: T;
  excerpt?: T;
  featuredImage?: T;
  status?: T;
  publishedAt?: T;
  author?: T;
  seo?:
    | T
    | {
        metaTitle?: T;
        metaDescription?: T;
        metaImage?: T;
      };
  showInNavigation?: T;
  navigationOrder?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors_select".
 */
export interface AuthorsSelect<T extends boolean = true> {
  tenant?: T;
  name?: T;
  email?: T;
  bio?: T;
  avatar?: T;
  role?: T;
  socialLinks?:
    | T
    | {
        twitter?: T;
        linkedin?: T;
        website?: T;
      };
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  tenant?: T;
  name?: T;
  slug?: T;
  description?: T;
  color?: T;
  featuredImage?: T;
  parent?: T;
  isVisible?: T;
  sortOrder?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags_select".
 */
export interface TagsSelect<T extends boolean = true> {
  tenant?: T;
  name?: T;
  slug?: T;
  description?: T;
  color?: T;
  isVisible?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants_select".
 */
export interface TenantsSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  domain?: T;
  domains?:
    | T
    | {
        domain?: T;
        id?: T;
      };
  isActive?: T;
  themeConfig?:
    | T
    | {
        primaryColor?: T;
        logo?: T;
        favicon?: T;
      };
  seoDefaults?:
    | T
    | {
        title?: T;
        description?: T;
        keywords?: T;
        ogImage?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ad-campaigns_select".
 */
export interface AdCampaignsSelect<T extends boolean = true> {
  name?: T;
  sponsor?: T;
  status?: T;
  startDate?: T;
  endDate?: T;
  totalBudget?: T;
  targeting?:
    | T
    | {
        sites?: T;
        targetingType?: T;
        specificPosts?: T;
        specificPages?: T;
        categories?: T;
        tags?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ad-creatives_select".
 */
export interface AdCreativesSelect<T extends boolean = true> {
  title?: T;
  campaign?: T;
  type?: T;
  status?: T;
  textContent?:
    | T
    | {
        headline?: T;
        description?: T;
        callToAction?: T;
      };
  bannerContent?:
    | T
    | {
        image?: T;
        altText?: T;
      };
  link?:
    | T
    | {
        url?: T;
        utmSource?: T;
        utmMedium?: T;
        utmCampaign?: T;
      };
  adminNotes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pricing-rules_select".
 */
export interface PricingRulesSelect<T extends boolean = true> {
  name?: T;
  site?: T;
  placementType?: T;
  basePrice?: T;
  multipliers?:
    | T
    | {
        weekendMultiplier?: T;
        holidayMultiplier?: T;
        peakSeasonMultiplier?: T;
        demandMultiplier?: T;
      };
  minimums?:
    | T
    | {
        minimumDays?: T;
        minimumBudget?: T;
      };
  discounts?:
    | T
    | {
        weeklyDiscount?: T;
        monthlyDiscount?: T;
        quarterlyDiscount?: T;
      };
  isActive?: T;
  priority?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}