import { getPayload } from 'payload'
import config from '../src/payload.config'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function seedPosts() {
  try {
    console.log('Starting to seed posts...')
    console.log('PAYLOAD_SECRET exists:', !!process.env.PAYLOAD_SECRET)
    console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL)

    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Get the first tenant (site)
    const tenants = await payload.find({
      collection: 'tenants',
      limit: 1,
    })

    if (tenants.docs.length === 0) {
      console.error('No tenants found. Please create a site first.')
      return
    }

    const tenant = tenants.docs[0]
    console.log(`Found tenant: ${tenant.name} (${tenant.id})`)

    // Create an author first
    const author = await payload.create({
      collection: 'authors',
      data: {
        tenant: tenant.id,
        name: '<PERSON>',
        email: '<EMAIL>',
        bio: 'Tech journalist with 10+ years of experience covering emerging technologies, AI, and digital innovation. Passionate about making complex tech topics accessible to everyone.',
        role: 'author',
        socialLinks: {
          twitter: 'https://twitter.com/alexjohnsontech',
          linkedin: 'https://linkedin.com/in/alexjohnsontech',
        },
        isActive: true,
      },
    })

    console.log(`Created author: ${author.name} (${author.id})`)

    // Create some categories
    const techCategory = await payload.create({
      collection: 'categories',
      data: {
        tenant: tenant.id,
        name: 'Technology',
        slug: 'technology',
        description: 'Latest technology news and trends',
        color: '#3B82F6',
      },
    })

    const aiCategory = await payload.create({
      collection: 'categories',
      data: {
        tenant: tenant.id,
        name: 'Artificial Intelligence',
        slug: 'ai',
        description: 'AI developments and breakthroughs',
        color: '#8B5CF6',
      },
    })

    console.log(`Created categories: ${techCategory.name}, ${aiCategory.name}`)

    // Create some tags
    const innovationTag = await payload.create({
      collection: 'tags',
      data: {
        tenant: tenant.id,
        name: 'Innovation',
        slug: 'innovation',
      },
    })

    const startupTag = await payload.create({
      collection: 'tags',
      data: {
        tenant: tenant.id,
        name: 'Startup',
        slug: 'startup',
      },
    })

    console.log(`Created tags: ${innovationTag.name}, ${startupTag.name}`)

    // Create test posts
    const posts = [
      {
        title: 'The Future of AI: Revolutionary Breakthroughs in 2025',
        slug: 'future-of-ai-2025',
        excerpt:
          'Discover the groundbreaking AI developments that are reshaping industries and transforming how we work, live, and interact with technology.',
        content: [
          {
            type: 'paragraph',
            children: [
              {
                text: 'Artificial Intelligence continues to evolve at an unprecedented pace, with 2025 marking a pivotal year for breakthrough innovations that promise to revolutionize multiple industries.',
              },
            ],
          },
          {
            type: 'paragraph',
            children: [
              {
                text: 'From advanced language models to autonomous systems, the latest developments in AI are not just incremental improvements—they represent fundamental shifts in how machines understand and interact with the world.',
              },
            ],
          },
        ],
        author: author.id,
        categories: [aiCategory.id, techCategory.id],
        tags: [innovationTag.id],
        status: 'published',
        publishedAt: new Date().toISOString(),
        isFeatured: true,
        readingTime: 5,
        tenant: tenant.id,
      },
      {
        title: 'Startup Ecosystem Thrives: $50B in Funding This Quarter',
        slug: 'startup-funding-q1-2025',
        excerpt:
          'The startup ecosystem shows remarkable resilience with record-breaking funding rounds across tech, healthcare, and clean energy sectors.',
        content: [
          {
            type: 'paragraph',
            children: [
              {
                text: 'The first quarter of 2025 has witnessed an extraordinary surge in startup funding, with venture capital firms investing over $50 billion across various sectors.',
              },
            ],
          },
          {
            type: 'paragraph',
            children: [
              {
                text: 'This represents a 35% increase compared to the same period last year, signaling renewed confidence in innovative technologies and disruptive business models.',
              },
            ],
          },
        ],
        author: author.id,
        categories: [techCategory.id],
        tags: [startupTag.id, innovationTag.id],
        status: 'published',
        publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        isFeatured: false,
        readingTime: 3,
        tenant: tenant.id,
      },
      {
        title: 'Quantum Computing Reaches New Milestone',
        slug: 'quantum-computing-milestone-2025',
        excerpt:
          'Scientists achieve quantum supremacy in practical applications, opening doors to revolutionary computing capabilities.',
        content: [
          {
            type: 'paragraph',
            children: [
              {
                text: 'A team of researchers has successfully demonstrated quantum supremacy in solving real-world optimization problems, marking a significant milestone in quantum computing development.',
              },
            ],
          },
        ],
        author: author.id,
        categories: [techCategory.id],
        tags: [innovationTag.id],
        status: 'published',
        publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        isFeatured: false,
        readingTime: 4,
        tenant: tenant.id,
      },
      {
        title: 'Sustainable Tech: Green Innovation Leads the Way',
        slug: 'sustainable-tech-green-innovation',
        excerpt:
          'Environmental technology startups are pioneering solutions for climate change while building profitable businesses.',
        content: [
          {
            type: 'paragraph',
            children: [
              {
                text: 'The intersection of technology and sustainability is creating unprecedented opportunities for innovation, with green tech startups leading the charge toward a more sustainable future.',
              },
            ],
          },
        ],
        author: author.id,
        categories: [techCategory.id],
        tags: [innovationTag.id, startupTag.id],
        status: 'published',
        publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
        isFeatured: false,
        readingTime: 6,
        tenant: tenant.id,
      },
      {
        title: 'The Rise of Edge Computing in IoT Applications',
        slug: 'edge-computing-iot-applications',
        excerpt:
          'Edge computing is transforming IoT deployments, enabling real-time processing and reducing latency for critical applications.',
        content: [
          {
            type: 'paragraph',
            children: [
              {
                text: 'As Internet of Things (IoT) devices proliferate across industries, edge computing has emerged as a crucial technology for processing data closer to its source.',
              },
            ],
          },
        ],
        author: author.id,
        categories: [techCategory.id],
        tags: [innovationTag.id],
        status: 'published',
        publishedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
        isFeatured: false,
        readingTime: 7,
        tenant: tenant.id,
      },
    ]

    // Create all posts
    for (const postData of posts) {
      const post = await payload.create({
        collection: 'posts',
        data: postData,
      })
      console.log(`Created post: ${post.title} (${post.id})`)
    }

    console.log('✅ Successfully seeded posts!')
  } catch (error) {
    console.error('❌ Error seeding posts:', error)
  }
}

// Run the seed function
seedPosts()
  .then(() => {
    console.log('Seed script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Seed script failed:', error)
    process.exit(1)
  })
